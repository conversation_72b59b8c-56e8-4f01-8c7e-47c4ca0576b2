[package]
name = "tauri-plugin-mpv"
version = "0.1.0"
authors = ["You"]
description = ""
edition = "2021"
rust-version = "1.77.2"
exclude = ["/examples", "/dist-js", "/guest-js", "/node_modules"]
links = "tauri-plugin-mpv"

[dependencies]
tauri = { version = "2" }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
thiserror = "2"
raw-window-handle = "0.6"

[target.'cfg(windows)'.dependencies]

[target.'cfg(unix)'.dependencies]

[build-dependencies]
tauri-plugin = { version = "2", features = ["build"] }
