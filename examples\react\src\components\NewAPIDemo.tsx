/**
 * New API Demo Component
 * 
 * This component demonstrates the new object-based initializeMpv API
 * that eliminates the need for undefined parameters.
 */

import React, { useState } from 'react';
import { initializeMpv, COMMON_PROPERTIES, MpvConfig } from 'tauri-plugin-mpv';

interface APIExample {
  name: string;
  description: string;
  code: string;
  execute: () => Promise<void>;
}

export function NewAPIDemo() {
  const [results, setResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const examples: APIExample[] = [
    {
      name: 'Default Initialization',
      description: 'Initialize with all defaults - no parameters needed!',
      code: `await initializeMpv();`,
      execute: async () => {
        const windowLabel = await initializeMpv();
        addResult(`✅ Default init completed. Window: ${windowLabel}`);
      }
    },
    {
      name: 'Custom Properties',
      description: 'Specify only the properties you want to observe',
      code: `await initializeMpv({
  observedProperties: ['filename', 'pause', 'time-pos']
});`,
      execute: async () => {
        const windowLabel = await initializeMpv({
          observedProperties: ['filename', 'pause', 'time-pos']
        });
        addResult(`✅ Custom properties init completed. Window: ${windowLabel}`);
      }
    },
    {
      name: 'Window Label Only',
      description: 'Specify target window without other parameters',
      code: `await initializeMpv({
  windowLabel: 'player'
});`,
      execute: async () => {
        const windowLabel = await initializeMpv({
          windowLabel: 'player'
        });
        addResult(`✅ Window label init completed. Window: ${windowLabel}`);
      }
    },
    {
      name: 'MPV Config Only',
      description: 'Apply MPV configuration without specifying other options',
      code: `await initializeMpv({
  mpvConfig: {
    'vo': 'gpu',
    'hwdec': 'auto',
    'volume': 75
  }
});`,
      execute: async () => {
        const windowLabel = await initializeMpv({
          mpvConfig: {
            'vo': 'gpu',
            'hwdec': 'auto',
            'volume': 75
          }
        });
        addResult(`✅ Config-only init completed. Window: ${windowLabel}`);
      }
    },
    {
      name: 'Full Configuration',
      description: 'Complete setup with all options',
      code: `await initializeMpv({
  observedProperties: COMMON_PROPERTIES,
  windowLabel: 'main-player',
  mpvConfig: {
    'vo': 'gpu',
    'hwdec': 'auto',
    'volume': 80,
    'loop-file': 'inf',
    'keep-open': true
  }
});`,
      execute: async () => {
        const windowLabel = await initializeMpv({
          observedProperties: COMMON_PROPERTIES,
          windowLabel: 'main-player',
          mpvConfig: {
            'vo': 'gpu',
            'hwdec': 'auto',
            'volume': 80,
            'loop-file': 'inf',
            'keep-open': true
          }
        });
        addResult(`✅ Full config init completed. Window: ${windowLabel}`);
      }
    },
    {
      name: 'High Quality Setup',
      description: 'Predefined high-quality video configuration',
      code: `await initializeMpv({
  mpvConfig: {
    'vo': 'gpu',
    'hwdec': 'auto',
    'profile': 'gpu-hq',
    'scale': 'ewa_lanczossharp',
    'interpolation': true
  }
});`,
      execute: async () => {
        const windowLabel = await initializeMpv({
          mpvConfig: {
            'vo': 'gpu',
            'hwdec': 'auto',
            'profile': 'gpu-hq',
            'scale': 'ewa_lanczossharp',
            'interpolation': true
          }
        });
        addResult(`✅ High quality init completed. Window: ${windowLabel}`);
      }
    }
  ];

  const executeExample = async (example: APIExample) => {
    setIsLoading(true);
    try {
      addResult(`🚀 Executing: ${example.name}`);
      await example.execute();
    } catch (error) {
      addResult(`❌ Error in ${example.name}: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', maxWidth: '1200px' }}>
      <h2>New Object-Based initializeMpv API Demo</h2>
      
      <div style={{ 
        backgroundColor: '#e7f3ff', 
        padding: '15px', 
        borderRadius: '8px', 
        marginBottom: '20px',
        border: '1px solid #b3d9ff'
      }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#0066cc' }}>✨ What's New?</h3>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li><strong>No more undefined parameters!</strong> Use object syntax instead</li>
          <li><strong>Cleaner API:</strong> Only specify the options you need</li>
          <li><strong>Better TypeScript support:</strong> Full type safety and intellisense</li>
          <li><strong>Backward compatible:</strong> Old API still works</li>
        </ul>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
        {/* Examples */}
        <div>
          <h3>API Examples</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
            {examples.map((example, index) => (
              <div key={index} style={{
                border: '1px solid #ddd',
                borderRadius: '8px',
                padding: '15px',
                backgroundColor: '#f9f9f9'
              }}>
                <h4 style={{ margin: '0 0 8px 0', color: '#333' }}>{example.name}</h4>
                <p style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#666' }}>
                  {example.description}
                </p>
                <pre style={{
                  backgroundColor: '#f0f0f0',
                  padding: '10px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  overflow: 'auto',
                  margin: '0 0 10px 0',
                  border: '1px solid #e0e0e0'
                }}>
                  {example.code}
                </pre>
                <button
                  onClick={() => executeExample(example)}
                  disabled={isLoading}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: isLoading ? 'not-allowed' : 'pointer',
                    opacity: isLoading ? 0.6 : 1
                  }}
                >
                  {isLoading ? 'Running...' : 'Run Example'}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Results */}
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
            <h3 style={{ margin: 0 }}>Execution Results</h3>
            <button
              onClick={clearResults}
              style={{
                padding: '6px 12px',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Clear
            </button>
          </div>
          <div style={{
            border: '1px solid #ddd',
            borderRadius: '8px',
            padding: '15px',
            backgroundColor: '#f8f9fa',
            height: '600px',
            overflowY: 'auto',
            fontFamily: 'monospace',
            fontSize: '12px'
          }}>
            {results.length === 0 ? (
              <div style={{ color: '#666', fontStyle: 'italic' }}>
                Click "Run Example" buttons to see results here...
              </div>
            ) : (
              results.map((result, index) => (
                <div key={index} style={{ 
                  marginBottom: '5px',
                  padding: '2px 0',
                  borderBottom: '1px solid #eee'
                }}>
                  {result}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Comparison */}
      <div style={{ marginTop: '30px' }}>
        <h3>API Comparison</h3>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
          <div>
            <h4 style={{ color: '#dc3545' }}>❌ Old API (Still Works)</h4>
            <pre style={{
              backgroundColor: '#fff5f5',
              padding: '15px',
              borderRadius: '8px',
              fontSize: '12px',
              border: '1px solid #f5c6cb'
            }}>
{`// Lots of undefined parameters 😞
await initializeMpv(
  undefined,           // properties
  undefined,           // window label  
  { 'vo': 'gpu' }     // config
);

// Hard to read and error-prone
await initializeMpv(
  ['filename', 'pause'],
  'player',
  undefined            // forgot config
);`}
            </pre>
          </div>
          <div>
            <h4 style={{ color: '#28a745' }}>✅ New API (Recommended)</h4>
            <pre style={{
              backgroundColor: '#f0fff4',
              padding: '15px',
              borderRadius: '8px',
              fontSize: '12px',
              border: '1px solid #c3e6cb'
            }}>
{`// Clean and readable 😊
await initializeMpv({
  mpvConfig: { 'vo': 'gpu' }
});

// Only specify what you need
await initializeMpv({
  observedProperties: ['filename', 'pause'],
  windowLabel: 'player'
});`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
