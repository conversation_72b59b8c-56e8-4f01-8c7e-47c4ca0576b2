/**
 * Test JSDoc documentation display effects
 *
 * This file is used to test JSDoc intellisense effects in IDE.
 * Open this file in VS Code or other TypeScript-supported IDEs,
 * hover over function calls to see detailed documentation.
 */

import {
  initializeMpv,
  sendMpvCommand,
  setVideoMarginRatio,
  COMMON_PROPERTIES,
  FULL_PROPERTIES
} from 'tauri-plugin-mpv';

/**
 * Test function - Demonstrate JSDoc effects
 */
export async function testJSDocEffects() {
  // Test initializeMpv function
  // Hover over initializeMpv to see documentation
  const windowLabel1 = await initializeMpv();

  // Test call with parameters
  // Hover over COMMON_PROPERTIES to see documentation
  const windowLabel2 = await initializeMpv(COMMON_PROPERTIES);

  // Test multi-window call
  const windowLabel3 = await initializeMpv(FULL_PROPERTIES, 'player');

  // Test sendMpvCommand function
  // Hover over sendMpvCommand to see detailed command examples
  await sendMpvCommand({
    command: ['loadfile', '/path/to/video.mp4']
  });

  await sendMpvCommand({
    command: ['set_property', 'pause', false]
  });

  await sendMpvCommand({
    command: ['seek', 30, 'absolute']
  });

  // Test setVideoMarginRatio function
  // Hover over setVideoMarginRatio to see margin setting examples
  await setVideoMarginRatio({
    bottom: 0.1
  });

  await setVideoMarginRatio({
    left: 0.05,
    right: 0.05,
    top: 0.1,
    bottom: 0.15
  });

  console.log('All function calls completed, window labels:', {
    windowLabel1,
    windowLabel2,
    windowLabel3
  });
}

/**
 * Demonstrate error handling
 */
export async function testErrorHandling() {
  try {
    // These calls may fail, demonstrating error handling
    await initializeMpv(['invalid-property']);
    await sendMpvCommand({ command: ['invalid-command'] });
  } catch (error) {
    console.error('MPV operation failed:', error);
    // Error handling approach mentioned in JSDoc
  }
}

/**
 * Demonstrate preset configuration usage
 */
export function demonstratePresets() {
  // Hover over these constants to see property list documentation
  console.log('Common properties:', COMMON_PROPERTIES);
  console.log('Full properties:', FULL_PROPERTIES);

  // These constants contain detailed comments explaining each property's purpose
  const basicProperties = [
    'filename',   // Current filename
    'pause',      // Pause state
    'time-pos',   // Playback position (seconds)
    'duration'    // Total duration (seconds)
  ];

  return basicProperties;
}

/**
 * Multi-window application example
 */
export async function multiWindowExample() {
  // Main window - only monitor basic information
  const mainWindow = await initializeMpv(['filename', 'pause'], 'main');

  // Player window - monitor full information
  const playerWindow = await initializeMpv(FULL_PROPERTIES, 'player');

  // Settings window - monitor minimal information
  const settingsWindow = await initializeMpv(['filename'], 'settings');

  return {
    mainWindow,
    playerWindow,
    settingsWindow
  };
}
