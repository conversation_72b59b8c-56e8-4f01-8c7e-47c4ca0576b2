use serde::de::DeserializeOwned;
use tauri::{
    plugin::{<PERSON>lug<PERSON><PERSON><PERSON>, PluginH<PERSON>le},
    AppHandle, Runtime,
};

use crate::models::*;

#[cfg(target_os = "ios")]
tauri::ios_plugin_binding!(init_plugin_mpv);

pub fn init<R: Runtime, C: DeserializeOwned>(
    _app: &AppHandle<R>,
    api: PluginApi<R, C>,
) -> crate::Result<Mpv<R>> {
    #[cfg(target_os = "android")]
    let handle = api.register_android_plugin("", "ExamplePlugin")?;
    #[cfg(target_os = "ios")]
    let handle = api.register_ios_plugin(init_plugin_mpv)?;
    Ok(Mpv(handle))
}

pub struct Mpv<R: Runtime>(PluginHandle<R>);

impl<R: Runtime> Mpv<R> {
    pub fn ping(&self, payload: PingRequest) -> crate::Result<PingResponse> {
        self.0
            .run_mobile_plugin("ping", payload)
            .map_err(Into::into)
    }
}
