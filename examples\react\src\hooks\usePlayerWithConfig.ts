/**
 * Enhanced MPV Player Hook with Configuration Support
 * 
 * This hook demonstrates how to use the new MPV configuration feature
 * to customize player behavior during initialization.
 */

import { listen } from '@tauri-apps/api/event';
import { useEffect, useState } from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';
import {
  initializeMpv,
  sendMpvCommand,
  COMMON_PROPERTIES,
  MpvConfig,
  MpvInitOptions
} from 'tauri-plugin-mpv';

interface MpvEvent {
  event_type: string;
  name?: string;
  data?: any;
}

interface PlayerState {
  connection: 'pending' | 'connected' | 'error';
  isPaused: boolean;
  currentFile: string | null;
  playlist: any[];
  eofReached: boolean;
  timePos: number;
  duration: number;
  volume: number;
  mute: boolean;
  speed: number;
  percentPos: number;
}

interface PlayerActions {
  loadFile: (file: string) => Promise<void>;
  play: () => Promise<void>;
  pause: () => Promise<void>;
  stop: () => Promise<void>;
  seek: (value: number) => Promise<void>;
  setVolume: (volume: number) => Promise<void>;
  setSpeed: (speed: number) => Promise<void>;
  toggleMute: () => Promise<void>;
}

type Player = PlayerState & PlayerActions;

/**
 * Enhanced usePlayer hook with MPV configuration support
 * 
 * @param mpvConfig - Optional MPV configuration options
 * @returns Player state and control functions
 */
export function usePlayerWithConfig(mpvConfig?: MpvConfig): Player {
  const [state, setState] = useState<PlayerState>({
    connection: 'pending',
    isPaused: true,
    currentFile: null,
    playlist: [],
    eofReached: false,
    timePos: 0,
    duration: 0,
    volume: 100,
    mute: false,
    speed: 1.0,
    percentPos: 0,
  });

  // Initialize MPV with configuration
  useEffect(() => {
    const initMpv = async () => {
      try {
        console.log('🎬 Initializing MPV with config:', mpvConfig);

        const windowLabel = await initializeMpv({
          observedProperties: Array.from(COMMON_PROPERTIES),
          mpvConfig
        });

        console.log('🎬 MPV initialization completed for window:', windowLabel);
        setState(prev => ({ ...prev, connection: 'connected' }));
      } catch (error) {
        console.error('🎬 MPV initialization failed:', error);
        setState(prev => ({ ...prev, connection: 'error' }));
      }
    };

    initMpv();
  }, [mpvConfig]);

  // Listen for MPV events
  useEffect(() => {
    const unlisten = listen<MpvEvent>('mpv-event', (event) => {
      const { event_type, name, data } = event.payload;

      if (event_type === 'property-change' && name) {
        setState(prev => {
          const newState = { ...prev };

          switch (name) {
            case 'filename':
              newState.currentFile = data;
              break;
            case 'pause':
              newState.isPaused = data;
              break;
            case 'time-pos':
              newState.timePos = typeof data === 'number' ? data : newState.timePos;
              break;
            case 'duration':
              newState.duration = typeof data === 'number' ? data : newState.duration;
              break;
            case 'volume':
              newState.volume = typeof data === 'number' ? data : newState.volume;
              break;
            case 'mute':
              newState.mute = typeof data === 'boolean' ? data : newState.mute;
              break;
            case 'speed':
              newState.speed = typeof data === 'number' ? data : newState.speed;
              break;
            case 'percent-pos':
              newState.percentPos = typeof data === 'number' ? data : newState.percentPos;
              break;
            case 'playlist':
              newState.playlist = data || [];
              break;
            case 'eof-reached':
              newState.eofReached = data || false;
              break;
            default:
              console.log('property-change', name, data);
              break;
          }

          return newState;
        });
      }
    });

    return () => {
      unlisten.then(fn => fn());
    };
  }, []);

  // Player control functions
  const loadFile = async (file: string) => {
    await sendMpvCommand({ command: ['loadfile', file] });
  };

  const play = async () => {
    await sendMpvCommand({ command: ['set_property', 'pause', false] });
  };

  const pause = async () => {
    await sendMpvCommand({ command: ['set_property', 'pause', true] });
  };

  const stop = async () => {
    await sendMpvCommand({ command: ['stop'] });
  };

  const seek = async (value: number) => {
    await sendMpvCommand({ command: ['seek', value, 'absolute'] });
  };

  const setVolume = async (volume: number) => {
    await sendMpvCommand({ command: ['set_property', 'volume', volume] });
  };

  const setSpeed = async (speed: number) => {
    await sendMpvCommand({ command: ['set_property', 'speed', speed] });
  };

  const toggleMute = async () => {
    await sendMpvCommand({ command: ['cycle', 'mute'] });
  };

  return {
    ...state,
    loadFile,
    play,
    pause,
    stop,
    seek,
    setVolume,
    setSpeed,
    toggleMute,
  };
}

// Predefined MPV configurations for common use cases

/**
 * High-quality video playback configuration
 */
export const HIGH_QUALITY_CONFIG: MpvConfig = {
  'vo': 'gpu',
  'hwdec': 'auto',
  'profile': 'gpu-hq',
  'scale': 'ewa_lanczossharp',
  'cscale': 'ewa_lanczossharp',
  'video-sync': 'display-resample',
  'interpolation': true,
  'tscale': 'oversample',
};

/**
 * Low-latency streaming configuration
 */
export const LOW_LATENCY_CONFIG: MpvConfig = {
  'vo': 'gpu',
  'hwdec': 'auto',
  'cache': false,
  'demuxer-max-bytes': '150MiB',
  'demuxer-readahead-secs': 5,
  'force-seekable': false,
};

/**
 * Audio-focused configuration
 */
export const AUDIO_FOCUSED_CONFIG: MpvConfig = {
  'vo': 'null',
  'audio-channels': 'stereo',
  'volume-max': 150,
  'audio-normalize-downmix': true,
  'audio-pitch-correction': true,
};

/**
 * Subtitle-enhanced configuration
 */
export const SUBTITLE_CONFIG: MpvConfig = {
  'sub-auto': 'fuzzy',
  'sub-file-paths': 'ass:srt:sub:subs:subtitles',
  'sub-font-size': 55,
  'sub-color': '#FFFFFFFF',
  'sub-border-color': '#FF000000',
  'sub-border-size': 3.2,
  'sub-shadow-offset': 1,
  'sub-shadow-color': '#33000000',
};

/**
 * Performance-optimized configuration for lower-end devices
 */
export const PERFORMANCE_CONFIG: MpvConfig = {
  'vo': 'gpu',
  'hwdec': 'auto',
  'profile': 'fast',
  'scale': 'bilinear',
  'dscale': 'bilinear',
  'cscale': 'bilinear',
  'video-sync': 'audio',
  'framedrop': 'vo',
};
