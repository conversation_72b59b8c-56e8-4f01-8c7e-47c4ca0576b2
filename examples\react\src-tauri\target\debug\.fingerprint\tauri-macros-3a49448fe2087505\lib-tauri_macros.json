{"rustc": 3062648155896360161, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 6112024264108573052, "deps": [[3060637413840920116, "proc_macro2", false, 14671332852158113395], [3412097196613774653, "tauri_codegen", false, 8549098172447985306], [13077543566650298139, "heck", false, 14180315616191779761], [17233053221795943287, "tauri_utils", false, 6662717997182966250], [17990358020177143287, "quote", false, 5671817847732845486], [18149961000318489080, "syn", false, 6678047537993184915]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-3a49448fe2087505\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}