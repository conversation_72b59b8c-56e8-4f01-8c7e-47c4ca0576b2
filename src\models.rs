use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct MpvEvent {
    pub event_type: String,
    pub name: Option<String>,
    pub data: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct MpvCommand {
    pub command: Vec<serde_json::Value>,
}

#[derive(<PERSON><PERSON>, Serialize, Deserialize, Debug)]
pub struct VideoMarginRatio {
    pub left: Option<f64>,
    pub right: Option<f64>,
    pub top: Option<f64>,
    pub bottom: Option<f64>,
}
