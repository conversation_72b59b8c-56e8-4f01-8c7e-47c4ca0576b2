use crate::{ipc, models::VideoMarginRatio};

pub fn send_mpv_command(command_json: &str) -> crate::Result<String> {
    ipc::send_command(command_json)
}

pub fn set_video_margin_ratio(ratio: VideoMarginRatio) -> crate::Result<()> {
    println!("Received video_margin_ratio: {:?}", ratio);

    if let Some(left) = ratio.left {
        let command = format!(
            r#"{{"command": ["set_property", "video-margin-ratio-left", {}]}}"#,
            left
        );
        send_mpv_command(&command)?;
    }

    if let Some(right) = ratio.right {
        let command = format!(
            r#"{{"command": ["set_property", "video-margin-ratio-right", {}]}}"#,
            right
        );
        send_mpv_command(&command)?;
    }

    if let Some(top) = ratio.top {
        let command = format!(
            r#"{{"command": ["set_property", "video-margin-ratio-top", {}]}}"#,
            top
        );
        send_mpv_command(&command)?;
    }

    if let Some(bottom) = ratio.bottom {
        let command = format!(
            r#"{{"command": ["set_property", "video-margin-ratio-bottom", {}]}}"#,
            bottom
        );
        send_mpv_command(&command)?;
    }

    Ok(())
}
