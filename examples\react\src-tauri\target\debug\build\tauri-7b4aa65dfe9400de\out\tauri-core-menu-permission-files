["\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\menu\\autogenerated\\default.toml"]