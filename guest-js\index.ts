import { invoke } from '@tauri-apps/api/core'
import { getCurrentWindow } from '@tauri-apps/api/window';

/**
 * MPV command interface
 * @interface MpvCommand
 */
export interface MpvCommand {
  /** MPV command array containing command name and parameters */
  command: (string | boolean | number)[];
}

/**
 * Video margin ratio configuration
 * @interface VideoMarginRatio
 */
export interface VideoMarginRatio {
  /** Left margin ratio (0-1) */
  left?: number;
  /** Right margin ratio (0-1) */
  right?: number;
  /** Top margin ratio (0-1) */
  top?: number;
  /** Bottom margin ratio (0-1) */
  bottom?: number;
}

/**
 * MPV configuration options
 * @interface MpvConfig
 */
export interface MpvConfig {
  /** MPV configuration options as key-value pairs */
  [key: string]: string | number | boolean;
}

/**
 * Common MPV properties list
 *
 * @description
 * Contains properties for playback control, audio control, and basic status information.
 * Suitable for most player applications, providing basic playback functionality monitoring.
 *
 * @constant
 * @readonly
 */
export const COMMON_PROPERTIES = [
  'playlist',      // Playlist
  'filename',      // Current filename
  'pause',         // Pause state
  'eof-reached',   // End of file reached state
  'time-pos',      // Playback position (seconds)
  'duration',      // Total duration (seconds)
  'volume',        // Volume (0-100)
  'mute',          // Mute state
  'speed',         // Playback speed
  'percent-pos'    // Playback progress percentage
] as const;

/**
 * Full MPV properties list
 *
 * @description
 * Contains all commonly used properties for playback, video, audio, subtitles, and chapters.
 * Suitable for advanced player applications that need complete functionality.
 *
 * @constant
 * @readonly
 */
export const FULL_PROPERTIES = [
  'playlist',        // Playlist
  'filename',        // Current filename
  'pause',           // Pause state
  'eof-reached',     // End of file reached state
  'time-pos',        // Playback position (seconds)
  'duration',        // Total duration (seconds)
  'volume',          // Volume (0-100)
  'mute',            // Mute state
  'speed',           // Playback speed
  'percent-pos',     // Playback progress percentage
  'loop-file',       // Single file loop
  'loop-playlist',   // Playlist loop
  'width',           // Video width
  'height',          // Video height
  'fps',             // Frame rate
  'video-bitrate',   // Video bitrate
  'audio-delay',     // Audio delay
  'sub-text',        // Current subtitle text
  'sub-delay',       // Subtitle delay
  'sid',             // Subtitle track ID
  'chapter',         // Current chapter
  'chapters',        // Chapter list
  'playback-time'    // Playback time (more precise)
] as const;

/**
 * Initialize MPV player
 *
 * @description
 * Initialize MPV player instance, set properties to observe, configure MPV options, and return the window label.
 * If no properties list is specified, COMMON_PROPERTIES will be used as default.
 * If no window label is specified, the current window's label will be used automatically.
 * MPV configuration options can be provided to customize player behavior.
 *
 * @param {string[] | readonly string[]} [observedProperties] - Initialization options object or properties array (legacy)
 * @param {string} [windowLabel] - Target window label (legacy parameter)
 * @param {MpvConfig} [mpvConfig] - MPV configuration options (legacy parameter)
 * @returns {Promise<string>} Returns the actual window label used
 *
 * @example
 * ```typescript
 *
 * await initializeMpv();
 *
 * await initializeMpv({
 *   observedProperties: ['filename', 'pause', 'time-pos'],
 *   windowLabel: 'player',
 *   mpvConfig: {
 *     'vo': 'gpu',
 *     'hwdec': 'auto',
 *     'volume': 50
 *   }
 * });
 *
 * ```
 *
 * @throws {Error} Throws error when MPV initialization fails
 */
export async function initializeMpv(
  {
    observedProperties,
    windowLabel,
    mpvConfig,
  }: {
    observedProperties?: string[] | readonly string[],
    windowLabel?: string,
    mpvConfig?: MpvConfig,
  }
): Promise<string> {

  if (!observedProperties) {
    observedProperties = Array.from(COMMON_PROPERTIES);
  }

  if (!windowLabel) {
    const currentWindow = getCurrentWindow();
    windowLabel = currentWindow.label;
  }

  return await invoke<string>('plugin:mpv|initialize_mpv', {
    observedProperties,
    windowLabel: windowLabel,
    mpvConfig: mpvConfig,
  });
}

/**
 * Send MPV command
 *
 * @description
 * Send raw commands to MPV player. This is a low-level API that allows direct control of all MPV functionality.
 * Command format follows MPV's JSON IPC protocol.
 *
 * @param {MpvCommand} command - MPV command object
 * @param {(string|boolean|number)[]} command.command - Command array, first element is command name, followed by parameters
 * @returns {Promise<string>} MPV response result
 *
 * @example
 * ```typescript
 * // Play/pause
 * await sendMpvCommand({ command: ['set_property', 'pause', false] });
 * await sendMpvCommand({ command: ['set_property', 'pause', true] });
 *
 * // Load file
 * await sendMpvCommand({ command: ['loadfile', '/path/to/video.mp4'] });
 *
 * // Seek to position
 * await sendMpvCommand({ command: ['seek', 30, 'absolute'] });
 * await sendMpvCommand({ command: ['seek', 10, 'relative'] });
 *
 * // Set volume
 * await sendMpvCommand({ command: ['set_property', 'volume', 80] });
 *
 * // Get property
 * await sendMpvCommand({ command: ['get_property', 'duration'] });
 *
 * // Playlist operations
 * await sendMpvCommand({ command: ['playlist-next'] });
 * await sendMpvCommand({ command: ['playlist-prev'] });
 * ```
 *
 * @throws {Error} Throws error when command sending fails or MPV returns error
 *
 * @see {@link https://mpv.io/manual/master/#json-ipc} MPV JSON IPC Documentation
 */
export async function sendMpvCommand(command: MpvCommand): Promise<string> {
  const commandJson = JSON.stringify(command);
  return await invoke<string>('plugin:mpv|send_mpv_command', {
    commandJson,
  });
}

/**
 * Set video margin ratio
 *
 * @description
 * Set the margin ratio of video within the window, used to leave space around the video for UI controls.
 * Ratio values are decimals between 0-1, representing the proportion relative to window dimensions.
 *
 * @param {VideoMarginRatio} ratio - Margin ratio configuration object
 * @param {number} [ratio.left] - Left margin ratio (0-1)
 * @param {number} [ratio.right] - Right margin ratio (0-1)
 * @param {number} [ratio.top] - Top margin ratio (0-1)
 * @param {number} [ratio.bottom] - Bottom margin ratio (0-1)
 * @returns {Promise<void>} Promise with no return value
 *
 * @example
 * ```typescript
 * // Leave 10% space at bottom for control bar
 * await setVideoMarginRatio({ bottom: 0.1 });
 *
 * // Leave 5% space on left and right sides
 * await setVideoMarginRatio({ left: 0.05, right: 0.05 });
 *
 * // Leave margins on all sides
 * await setVideoMarginRatio({
 *   left: 0.05,
 *   right: 0.05,
 *   top: 0.1,
 *   bottom: 0.15
 * });
 *
 * // Reset margins (remove all margins)
 * await setVideoMarginRatio({
 *   left: 0,
 *   right: 0,
 *   top: 0,
 *   bottom: 0
 * });
 * ```
 *
 * @throws {Error} Throws error when setting fails
 */
export async function setVideoMarginRatio(ratio: VideoMarginRatio): Promise<void> {
  return await invoke<void>('plugin:mpv|set_video_margin_ratio', {
    ratio,
  });
}
