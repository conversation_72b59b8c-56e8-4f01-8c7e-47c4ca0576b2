cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\projects\mpv-tauri\examples\react\src-tauri\target\debug\build\tauri-plugin-cli-55e628d9c4d37b16\out\tauri-plugin-cli-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-cli-2.4.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
