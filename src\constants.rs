use std::sync::OnceLock;

#[cfg(windows)]
pub const IPC_PATH: &str = r"\\.\pipe\mpvsocket";
#[cfg(unix)]
pub const IPC_PATH: &str = "/tmp/mpvsocket";

pub static IPC_PATH_ONCELOCK: OnceLock<String> = OnceLock::new();

pub const OBSERVED_PROPERTIES: [&str; 10] = [
    "playlist",
    "filename",
    "pause",
    "eof-reached",
    "time-pos",
    "duration",
    "volume",
    "mute",
    "speed",
    "percent-pos",
];
