{"rustc": 3062648155896360161, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 15265854434579149077, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 16248698055440802291], [3060637413840920116, "proc_macro2", false, 14671332852158113395], [3129130049864710036, "memchr", false, 29019483509611702], [3150220818285335163, "url", false, 11840875569088997571], [3191507132440681679, "serde_untagged", false, 4621314262121485758], [4899080583175475170, "semver", false, 3226338932051080097], [5986029879202738730, "log", false, 7851226378520772143], [6213549728662707793, "serde_with", false, 3292683658237664212], [6606131838865521726, "ctor", false, 15761785601465401748], [6913375703034175521, "schemars", false, 1487061493263669198], [7170110829644101142, "json_patch", false, 15030312149260156537], [8319709847752024821, "uuid", false, 1706554566045737292], [9010263965687315507, "http", false, 10349939186187053695], [9451456094439810778, "regex", false, 8196568352413053487], [9556762810601084293, "brotli", false, 6605499704835004156], [9689903380558560274, "serde", false, 13680709243831299839], [10806645703491011684, "thiserror", false, 6613235972149669718], [11655476559277113544, "cargo_metadata", false, 13597231720379752781], [11989259058781683633, "dunce", false, 11550051946685015324], [12060164242600251039, "toml", false, 6586288843445032427], [13625485746686963219, "anyhow", false, 11686108123410396079], [14232843520438415263, "html5ever", false, 10448284280144640998], [15088007382495681292, "kuchiki", false, 5288451216510785390], [15367738274754116744, "serde_json", false, 8221918773140236630], [15622660310229662834, "walkdir", false, 16763487993090417615], [17146114186171651583, "infer", false, 14403959834817474969], [17155886227862585100, "glob", false, 3512897230383724311], [17186037756130803222, "phf", false, 4132992680105411749], [17990358020177143287, "quote", false, 5671817847732845486]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-be033012671a820d\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}