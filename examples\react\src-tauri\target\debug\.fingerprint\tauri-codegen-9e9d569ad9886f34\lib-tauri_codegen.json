{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 4382323575162997333, "deps": [[3060637413840920116, "proc_macro2", false, 14671332852158113395], [3150220818285335163, "url", false, 11840875569088997571], [4899080583175475170, "semver", false, 3226338932051080097], [7170110829644101142, "json_patch", false, 15030312149260156537], [7392050791754369441, "ico", false, 9246768449734793327], [8319709847752024821, "uuid", false, 1706554566045737292], [9556762810601084293, "brotli", false, 6605499704835004156], [9689903380558560274, "serde", false, 13680709243831299839], [9857275760291862238, "sha2", false, 17514700961481089571], [10806645703491011684, "thiserror", false, 6613235972149669718], [12687914511023397207, "png", false, 3834996804452332006], [13077212702700853852, "base64", false, 5447172291193198490], [15367738274754116744, "serde_json", false, 8221918773140236630], [15622660310229662834, "walkdir", false, 16763487993090417615], [17233053221795943287, "tauri_utils", false, 6662717997182966250], [17990358020177143287, "quote", false, 5671817847732845486], [18149961000318489080, "syn", false, 6678047537993184915]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-9e9d569ad9886f34\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}