["\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\path\\autogenerated\\default.toml"]