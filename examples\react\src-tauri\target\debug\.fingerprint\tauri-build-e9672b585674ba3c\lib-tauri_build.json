{"rustc": 3062648155896360161, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 2187796773536582234, "deps": [[4899080583175475170, "semver", false, 3226338932051080097], [6913375703034175521, "schemars", false, 1487061493263669198], [7170110829644101142, "json_patch", false, 15030312149260156537], [9689903380558560274, "serde", false, 13680709243831299839], [12060164242600251039, "toml", false, 6586288843445032427], [12714016054753183456, "tauri_winres", false, 10979200639630045127], [13077543566650298139, "heck", false, 14180315616191779761], [13475171727366188400, "cargo_toml", false, 10614753964149538802], [13625485746686963219, "anyhow", false, 11686108123410396079], [15367738274754116744, "serde_json", false, 8221918773140236630], [15622660310229662834, "walkdir", false, 16763487993090417615], [16928111194414003569, "dirs", false, 1929647843184694697], [17155886227862585100, "glob", false, 3512897230383724311], [17233053221795943287, "tauri_utils", false, 6662717997182966250]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-e9672b585674ba3c\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}