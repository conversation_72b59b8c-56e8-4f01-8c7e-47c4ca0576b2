["\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\tauri-7b4aa65dfe9400de\\out\\permissions\\tray\\autogenerated\\default.toml"]