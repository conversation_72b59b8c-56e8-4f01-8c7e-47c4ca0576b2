use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>uf<PERSON><PERSON><PERSON>, Write};

#[cfg(windows)]
use std::fs::OpenOptions;
#[cfg(unix)]
use std::os::unix::net::UnixStream;

use crate::constants::IPC_PATH_ONCELOCK;

pub fn set_ipc_path(window_handle: i64) {
    let ipc_path = format!("{}_{}", crate::constants::IPC_PATH, window_handle);
    println!("Setting IPC Path: {}", ipc_path);
    if let Err(_) = IPC_PATH_ONCELOCK.set(ipc_path.clone()) {
        println!(
            "IPC Path already set, current path: {:?}",
            IPC_PATH_ONCELOCK.get()
        );
    }
}

pub fn send_command(command_json: &str) -> crate::Result<String> {
    println!("Received command: {}", command_json);

    let ipc_path = IPC_PATH_ONCELOCK.get().unwrap();

    #[cfg(windows)]
    {
        let mut pipe = OpenOptions::new()
            .read(true)
            .write(true)
            .open(ipc_path)
            .map_err(|e| {
                crate::Error::IpcError(format!(
                    "Failed to open named pipe at '{}': {}",
                    ipc_path, e
                ))
            })?;

        pipe.write_all(command_json.as_bytes()).map_err(|e| {
            crate::Error::IpcError(format!("Failed to write command to named pipe: {}", e))
        })?;

        pipe.write_all(b"\n").map_err(|e| {
            crate::Error::IpcError(format!("Failed to write newline to named pipe: {}", e))
        })?;

        pipe.flush().map_err(|e| {
            crate::Error::IpcError(format!("Failed to flush named pipe: {}", e))
        })?;

        let mut reader = BufReader::new(pipe);
        let mut response = String::new();

        reader.read_line(&mut response).map_err(|e| {
            crate::Error::IpcError(format!("Failed to read response from named pipe: {}", e))
        })?;

        println!("Received response: {}", response);

        Ok(response)
    }

    #[cfg(unix)]
    {
        let mut stream = UnixStream::connect(ipc_path).map_err(|e| {
            crate::Error::IpcError(format!(
                "Failed to connect to Unix socket at '{}': {}",
                ipc_path, e
            ))
        })?;

        stream.write_all(command_json.as_bytes()).map_err(|e| {
            crate::Error::IpcError(format!("Failed to write command to Unix socket: {}", e))
        })?;

        stream.write_all(b"\n").map_err(|e| {
            crate::Error::IpcError(format!("Failed to write newline to Unix socket: {}", e))
        })?;

        stream.flush().map_err(|e| {
            crate::Error::IpcError(format!("Failed to flush Unix socket: {}", e))
        })?;

        let mut reader = BufReader::new(stream);
        let mut response = String::new();

        reader.read_line(&mut response).map_err(|e| {
            crate::Error::IpcError(format!("Failed to read response from Unix socket: {}", e))
        })?;

        println!("Received response: {}", response);

        Ok(response)
    }
}
