{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 4699914731831446627, "deps": [[40386456601120721, "percent_encoding", false, 14148879443296491786], [1200537532907108615, "url<PERSON><PERSON>n", false, 12757436868618282934], [1260461579271933187, "serialize_to_javascript", false, 13559748600305340297], [2013030631243296465, "webview2_com", false, 9275405753544062218], [3150220818285335163, "url", false, 5562215927590538623], [3331586631144870129, "getrandom", false, 9532195832835361007], [4143744114649553716, "raw_window_handle", false, 7362618845288443667], [4494683389616423722, "muda", false, 6942989761422058449], [5986029879202738730, "log", false, 7851226378520772143], [6537120525306722933, "tauri_macros", false, 241721937087938103], [7303982924001358866, "tokio", false, 16479821694828158028], [8427153362654230442, "build_script_build", false, 17582842390601078653], [9010263965687315507, "http", false, 10349939186187053695], [9689903380558560274, "serde", false, 3450527659744886422], [9952368442187680820, "tauri_runtime_wry", false, 2329312541810500814], [10229185211513642314, "mime", false, 12362920499392777088], [10806645703491011684, "thiserror", false, 6613235972149669718], [11989259058781683633, "dunce", false, 11550051946685015324], [12565293087094287914, "window_vibrancy", false, 12379208497498110051], [12986574360607194341, "serde_repr", false, 16267330032471215763], [13077543566650298139, "heck", false, 14180315616191779761], [13116089016666501665, "windows", false, 2222720715033256199], [13625485746686963219, "anyhow", false, 11686108123410396079], [15367738274754116744, "serde_json", false, 17612069096979246765], [16727543399706004146, "cookie", false, 16595614870648452621], [16928111194414003569, "dirs", false, 526810929768162361], [17155886227862585100, "glob", false, 3512897230383724311], [17233053221795943287, "tauri_utils", false, 3243504842503761867], [18010483002580779355, "tauri_runtime", false, 7826976577989967672]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-0930a55bf7cf85af\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}