{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 17323626394537666148, "deps": [[376837177317575824, "softbuffer", false, 12140419127896264987], [1825855694502115139, "tao", false, 11644683176596742404], [2013030631243296465, "webview2_com", false, 9275405753544062218], [3150220818285335163, "url", false, 5562215927590538623], [3722963349756955755, "once_cell", false, 15688156465087749428], [4143744114649553716, "raw_window_handle", false, 7362618845288443667], [5986029879202738730, "log", false, 7851226378520772143], [8558698349995473911, "wry", false, 325563860334155724], [9010263965687315507, "http", false, 10349939186187053695], [9952368442187680820, "build_script_build", false, 14166297104105051397], [13116089016666501665, "windows", false, 2222720715033256199], [17233053221795943287, "tauri_utils", false, 3243504842503761867], [18010483002580779355, "tauri_runtime", false, 7826976577989967672]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-6a5baa726609d65b\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}