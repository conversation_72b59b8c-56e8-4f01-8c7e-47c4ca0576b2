/**
 * Test file for the new object-based initializeMpv API
 * 
 * This file demonstrates the new cleaner API that doesn't require undefined parameters.
 */

import { 
  initializeMpv, 
  COMMON_PROPERTIES, 
  FULL_PROPERTIES,
  MpvConfig,
  MpvInitOptions
} from 'tauri-plugin-mpv';

/**
 * Test the new object-based API
 */
export async function testNewAPI() {
  console.log('Testing new object-based initializeMpv API...');

  // 1. Default initialization (no parameters needed!)
  console.log('1. Default initialization:');
  const windowLabel1 = await initializeMpv();
  console.log('Window label:', windowLabel1);

  // 2. With custom properties only
  console.log('2. Custom properties only:');
  const windowLabel2 = await initializeMpv({
    observedProperties: ['filename', 'pause', 'time-pos']
  });
  console.log('Window label:', windowLabel2);

  // 3. With window label only
  console.log('3. Window label only:');
  const windowLabel3 = await initializeMpv({
    windowLabel: 'player'
  });
  console.log('Window label:', windowLabel3);

  // 4. With MPV config only
  console.log('4. MPV config only:');
  const windowLabel4 = await initializeMpv({
    mpvConfig: {
      'vo': 'gpu',
      'hwdec': 'auto',
      'volume': 75
    }
  });
  console.log('Window label:', windowLabel4);

  // 5. Full configuration
  console.log('5. Full configuration:');
  const windowLabel5 = await initializeMpv({
    observedProperties: FULL_PROPERTIES,
    windowLabel: 'main-player',
    mpvConfig: {
      'vo': 'gpu',
      'hwdec': 'auto',
      'profile': 'gpu-hq',
      'volume': 80,
      'loop-file': 'inf',
      'keep-open': true,
      'osd-level': 1,
      'sub-auto': 'fuzzy'
    }
  });
  console.log('Window label:', windowLabel5);
}

/**
 * Test legacy API (still works for backward compatibility)
 */
export async function testLegacyAPI() {
  console.log('Testing legacy positional parameters API...');

  // 1. Default
  const windowLabel1 = await initializeMpv();

  // 2. Properties only
  const windowLabel2 = await initializeMpv(['filename', 'pause']);

  // 3. Properties + window label
  const windowLabel3 = await initializeMpv(COMMON_PROPERTIES, 'player');

  // 4. All parameters
  const windowLabel4 = await initializeMpv(
    COMMON_PROPERTIES, 
    'player', 
    { 'vo': 'gpu', 'volume': 75 }
  );

  console.log('Legacy API test completed');
}

/**
 * Demonstrate different configuration scenarios
 */
export async function demonstrateConfigurations() {
  console.log('Demonstrating different configuration scenarios...');

  // High-quality video setup
  await initializeMpv({
    mpvConfig: {
      'vo': 'gpu',
      'hwdec': 'auto',
      'profile': 'gpu-hq',
      'scale': 'ewa_lanczossharp',
      'cscale': 'ewa_lanczossharp',
      'video-sync': 'display-resample',
      'interpolation': true
    }
  });

  // Audio-focused setup
  await initializeMpv({
    observedProperties: ['filename', 'pause', 'time-pos', 'volume', 'mute'],
    mpvConfig: {
      'vo': 'null',  // No video output
      'audio-channels': 'stereo',
      'volume-max': 150,
      'audio-normalize-downmix': true
    }
  });

  // Streaming setup
  await initializeMpv({
    mpvConfig: {
      'cache': true,
      'demuxer-max-bytes': '150MiB',
      'demuxer-readahead-secs': 5,
      'network-timeout': 30,
      'force-seekable': false
    }
  });

  // Development/debug setup
  await initializeMpv({
    mpvConfig: {
      'msg-level': 'all=debug',
      'osd-level': 3,
      'stats-file': 'mpv-stats.txt'
    }
  });
}

/**
 * Type-safe configuration builder
 */
export class MpvConfigBuilder {
  private config: MpvConfig = {};

  video(options: {
    output?: 'gpu' | 'direct3d' | 'opengl';
    hwdec?: 'auto' | 'no' | 'yes';
    profile?: 'gpu-hq' | 'fast' | 'high-quality';
  }) {
    if (options.output) this.config['vo'] = options.output;
    if (options.hwdec) this.config['hwdec'] = options.hwdec;
    if (options.profile) this.config['profile'] = options.profile;
    return this;
  }

  audio(options: {
    volume?: number;
    maxVolume?: number;
    channels?: 'mono' | 'stereo' | 'surround';
    normalize?: boolean;
  }) {
    if (options.volume) this.config['volume'] = options.volume;
    if (options.maxVolume) this.config['volume-max'] = options.maxVolume;
    if (options.channels) this.config['audio-channels'] = options.channels;
    if (options.normalize) this.config['audio-normalize-downmix'] = options.normalize;
    return this;
  }

  playback(options: {
    loop?: boolean | 'inf';
    keepOpen?: boolean;
    speed?: number;
    pause?: boolean;
  }) {
    if (options.loop !== undefined) {
      this.config['loop-file'] = options.loop;
    }
    if (options.keepOpen) this.config['keep-open'] = options.keepOpen;
    if (options.speed) this.config['speed'] = options.speed;
    if (options.pause !== undefined) this.config['pause'] = options.pause;
    return this;
  }

  subtitles(options: {
    autoLoad?: 'no' | 'exact' | 'fuzzy' | 'all';
    fontSize?: number;
    color?: string;
    borderSize?: number;
  }) {
    if (options.autoLoad) this.config['sub-auto'] = options.autoLoad;
    if (options.fontSize) this.config['sub-font-size'] = options.fontSize;
    if (options.color) this.config['sub-color'] = options.color;
    if (options.borderSize) this.config['sub-border-size'] = options.borderSize;
    return this;
  }

  build(): MpvConfig {
    return { ...this.config };
  }
}

/**
 * Example using the configuration builder
 */
export async function testConfigBuilder() {
  const config = new MpvConfigBuilder()
    .video({ output: 'gpu', hwdec: 'auto', profile: 'gpu-hq' })
    .audio({ volume: 75, maxVolume: 150, channels: 'stereo' })
    .playback({ loop: 'inf', keepOpen: true })
    .subtitles({ autoLoad: 'fuzzy', fontSize: 55 })
    .build();

  await initializeMpv({
    observedProperties: FULL_PROPERTIES,
    windowLabel: 'configured-player',
    mpvConfig: config
  });

  console.log('Configuration builder test completed');
}

/**
 * Utility function to create common configurations
 */
export const createMpvConfig = {
  highQuality: (): MpvInitOptions => ({
    mpvConfig: {
      'vo': 'gpu',
      'hwdec': 'auto',
      'profile': 'gpu-hq',
      'scale': 'ewa_lanczossharp',
      'interpolation': true
    }
  }),

  lowLatency: (): MpvInitOptions => ({
    mpvConfig: {
      'cache': false,
      'demuxer-readahead-secs': 1,
      'video-sync': 'audio'
    }
  }),

  audioOnly: (): MpvInitOptions => ({
    observedProperties: ['filename', 'pause', 'time-pos', 'volume'],
    mpvConfig: {
      'vo': 'null',
      'audio-channels': 'stereo'
    }
  }),

  streaming: (): MpvInitOptions => ({
    mpvConfig: {
      'cache': true,
      'demuxer-max-bytes': '50MiB',
      'network-timeout': 15
    }
  })
};

/**
 * Example usage of utility configurations
 */
export async function testUtilityConfigs() {
  // High quality setup
  await initializeMpv(createMpvConfig.highQuality());

  // Low latency setup
  await initializeMpv(createMpvConfig.lowLatency());

  // Audio only setup
  await initializeMpv(createMpvConfig.audioOnly());

  // Streaming setup
  await initializeMpv(createMpvConfig.streaming());

  console.log('Utility configurations test completed');
}
