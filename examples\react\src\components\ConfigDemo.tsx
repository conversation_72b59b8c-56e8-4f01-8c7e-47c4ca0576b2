/**
 * MPV Configuration Demo Component
 * 
 * This component demonstrates how to use different MPV configurations
 * for various use cases.
 */

import React, { useState } from 'react';
import { 
  usePlayerWithConfig,
  HIGH_QUALITY_CONFIG,
  LOW_LATENCY_CONFIG,
  AUDIO_FOCUSED_CONFIG,
  SUBTITLE_CONFIG,
  PERFORMANCE_CONFIG,
  MpvConfig
} from '../hooks/usePlayerWithConfig';

interface ConfigOption {
  name: string;
  description: string;
  config: MpvConfig;
}

const CONFIG_OPTIONS: ConfigOption[] = [
  {
    name: 'Default',
    description: 'Standard MPV configuration',
    config: {},
  },
  {
    name: 'High Quality',
    description: 'Best video quality with GPU acceleration and advanced scaling',
    config: HIGH_QUALITY_CONFIG,
  },
  {
    name: 'Low Latency',
    description: 'Optimized for streaming with minimal buffering',
    config: LOW_LATENCY_CONFIG,
  },
  {
    name: 'Audio Focused',
    description: 'Audio playback without video rendering',
    config: AUDIO_FOCUSED_CONFIG,
  },
  {
    name: 'Subtitle Enhanced',
    description: 'Optimized subtitle display and auto-loading',
    config: SUBTITLE_CONFIG,
  },
  {
    name: 'Performance',
    description: 'Fast playback for lower-end devices',
    config: PERFORMANCE_CONFIG,
  },
];

export function ConfigDemo() {
  const [selectedConfig, setSelectedConfig] = useState<ConfigOption>(CONFIG_OPTIONS[0]);
  const [customConfig, setCustomConfig] = useState<string>('');
  const [useCustom, setUseCustom] = useState(false);
  
  // Parse custom config JSON
  let parsedCustomConfig: MpvConfig = {};
  if (useCustom && customConfig) {
    try {
      parsedCustomConfig = JSON.parse(customConfig);
    } catch (error) {
      console.error('Invalid custom config JSON:', error);
    }
  }
  
  const finalConfig = useCustom ? parsedCustomConfig : selectedConfig.config;
  const player = usePlayerWithConfig(finalConfig);

  const handleConfigChange = (config: ConfigOption) => {
    setSelectedConfig(config);
    setUseCustom(false);
  };

  const handleCustomConfigToggle = () => {
    setUseCustom(!useCustom);
    if (!useCustom && !customConfig) {
      // Set example custom config
      setCustomConfig(JSON.stringify({
        'vo': 'gpu',
        'hwdec': 'auto',
        'volume': 75,
        'loop-file': 'inf',
        'keep-open': true
      }, null, 2));
    }
  };

  const loadSampleVideo = async () => {
    // You can replace this with an actual video file path
    const samplePath = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
    await player.loadFile(samplePath);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>MPV Configuration Demo</h2>
      
      {/* Connection Status */}
      <div style={{ 
        padding: '10px', 
        marginBottom: '20px',
        backgroundColor: player.connection === 'connected' ? '#d4edda' : 
                         player.connection === 'error' ? '#f8d7da' : '#fff3cd',
        border: '1px solid',
        borderColor: player.connection === 'connected' ? '#c3e6cb' : 
                     player.connection === 'error' ? '#f5c6cb' : '#ffeaa7',
        borderRadius: '4px'
      }}>
        Status: {player.connection}
      </div>

      {/* Configuration Selection */}
      <div style={{ marginBottom: '20px' }}>
        <h3>Select Configuration:</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '10px' }}>
          {CONFIG_OPTIONS.map((config) => (
            <button
              key={config.name}
              onClick={() => handleConfigChange(config)}
              style={{
                padding: '10px',
                border: '2px solid',
                borderColor: selectedConfig.name === config.name && !useCustom ? '#007bff' : '#ccc',
                borderRadius: '4px',
                backgroundColor: selectedConfig.name === config.name && !useCustom ? '#e7f3ff' : 'white',
                cursor: 'pointer',
                textAlign: 'left'
              }}
            >
              <div style={{ fontWeight: 'bold' }}>{config.name}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>{config.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Custom Configuration */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
          <input
            type="checkbox"
            checked={useCustom}
            onChange={handleCustomConfigToggle}
            style={{ marginRight: '8px' }}
          />
          Use Custom Configuration
        </label>
        
        {useCustom && (
          <div>
            <label style={{ display: 'block', marginBottom: '5px' }}>
              Custom MPV Config (JSON):
            </label>
            <textarea
              value={customConfig}
              onChange={(e) => setCustomConfig(e.target.value)}
              style={{
                width: '100%',
                height: '150px',
                fontFamily: 'monospace',
                fontSize: '12px',
                padding: '10px',
                border: '1px solid #ccc',
                borderRadius: '4px'
              }}
              placeholder="Enter MPV configuration as JSON..."
            />
          </div>
        )}
      </div>

      {/* Current Configuration Display */}
      <div style={{ marginBottom: '20px' }}>
        <h3>Current Configuration:</h3>
        <pre style={{
          backgroundColor: '#f8f9fa',
          padding: '10px',
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto',
          maxHeight: '200px'
        }}>
          {JSON.stringify(finalConfig, null, 2)}
        </pre>
      </div>

      {/* Player Controls */}
      <div style={{ marginBottom: '20px' }}>
        <h3>Player Controls:</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button onClick={loadSampleVideo} style={{ padding: '8px 16px' }}>
            Load Sample Video
          </button>
          <button onClick={player.play} disabled={player.connection !== 'connected'} style={{ padding: '8px 16px' }}>
            Play
          </button>
          <button onClick={player.pause} disabled={player.connection !== 'connected'} style={{ padding: '8px 16px' }}>
            Pause
          </button>
          <button onClick={player.stop} disabled={player.connection !== 'connected'} style={{ padding: '8px 16px' }}>
            Stop
          </button>
          <button onClick={player.toggleMute} disabled={player.connection !== 'connected'} style={{ padding: '8px 16px' }}>
            Toggle Mute
          </button>
        </div>
      </div>

      {/* Player State */}
      <div>
        <h3>Player State:</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
          <div>File: {player.currentFile || 'None'}</div>
          <div>Paused: {player.isPaused ? 'Yes' : 'No'}</div>
          <div>Time: {player.timePos.toFixed(1)}s / {player.duration.toFixed(1)}s</div>
          <div>Volume: {player.volume}%</div>
          <div>Speed: {player.speed}x</div>
          <div>Muted: {player.mute ? 'Yes' : 'No'}</div>
          <div>Progress: {player.percentPos.toFixed(1)}%</div>
        </div>
      </div>

      {/* Configuration Examples */}
      <div style={{ marginTop: '30px' }}>
        <h3>Configuration Examples:</h3>
        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>Video Quality Options</summary>
          <pre style={{ backgroundColor: '#f8f9fa', padding: '10px', fontSize: '12px' }}>
{`{
  "vo": "gpu",           // Use GPU-accelerated video output
  "hwdec": "auto",       // Enable hardware decoding
  "profile": "gpu-hq",   // High-quality GPU profile
  "scale": "ewa_lanczossharp",  // High-quality scaling
  "interpolation": true  // Frame interpolation
}`}
          </pre>
        </details>
        
        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>Audio Options</summary>
          <pre style={{ backgroundColor: '#f8f9fa', padding: '10px', fontSize: '12px' }}>
{`{
  "volume": 75,                    // Default volume
  "volume-max": 150,               // Maximum volume
  "audio-channels": "stereo",      // Audio channel layout
  "audio-normalize-downmix": true  // Normalize audio
}`}
          </pre>
        </details>
        
        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>Playback Options</summary>
          <pre style={{ backgroundColor: '#f8f9fa', padding: '10px', fontSize: '12px' }}>
{`{
  "loop-file": "inf",     // Loop current file infinitely
  "loop-playlist": true,  // Loop entire playlist
  "keep-open": true,      // Keep window open after playback
  "speed": 1.25          // Playback speed
}`}
          </pre>
        </details>
      </div>
    </div>
  );
}
