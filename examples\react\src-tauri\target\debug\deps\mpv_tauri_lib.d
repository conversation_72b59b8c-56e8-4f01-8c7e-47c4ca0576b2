D:\projects\mpv-tauri\examples\react\src-tauri\target\debug\deps\mpv_tauri_lib.d: src\lib.rs D:\projects\mpv-tauri\examples\react\src-tauri\target\debug\build\mpv_tauri-24a4140d2289e2c1\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\projects\mpv-tauri\examples\react\src-tauri\target\debug\deps\mpv_tauri_lib.lib: src\lib.rs D:\projects\mpv-tauri\examples\react\src-tauri\target\debug\build\mpv_tauri-24a4140d2289e2c1\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\projects\mpv-tauri\examples\react\src-tauri\target\debug\deps\mpv_tauri_lib.dll: src\lib.rs D:\projects\mpv-tauri\examples\react\src-tauri\target\debug\build\mpv_tauri-24a4140d2289e2c1\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\projects\mpv-tauri\examples\react\src-tauri\target\debug\deps\libmpv_tauri_lib.rlib: src\lib.rs D:\projects\mpv-tauri\examples\react\src-tauri\target\debug\build\mpv_tauri-24a4140d2289e2c1\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\lib.rs:
D:\projects\mpv-tauri\examples\react\src-tauri\target\debug\build\mpv_tauri-24a4140d2289e2c1\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=22
# env-dep:CARGO_PKG_DESCRIPTION=A example program for embedding MPV window in Tauri.
# env-dep:CARGO_PKG_NAME=mpv_tauri
# env-dep:OUT_DIR=D:\\projects\\mpv-tauri\\examples\\react\\src-tauri\\target\\debug\\build\\mpv_tauri-24a4140d2289e2c1\\out
