{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 15265854434579149077, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 12757436868618282934], [3129130049864710036, "memchr", false, 29019483509611702], [3150220818285335163, "url", false, 5562215927590538623], [3191507132440681679, "serde_untagged", false, 14850076823095035631], [4899080583175475170, "semver", false, 710181288610495963], [5986029879202738730, "log", false, 7851226378520772143], [6213549728662707793, "serde_with", false, 16838150718441458599], [6606131838865521726, "ctor", false, 15761785601465401748], [7170110829644101142, "json_patch", false, 9165434369824267578], [8319709847752024821, "uuid", false, 2868236799358494891], [9010263965687315507, "http", false, 10349939186187053695], [9451456094439810778, "regex", false, 8196568352413053487], [9556762810601084293, "brotli", false, 6605499704835004156], [9689903380558560274, "serde", false, 3450527659744886422], [10806645703491011684, "thiserror", false, 6613235972149669718], [11989259058781683633, "dunce", false, 11550051946685015324], [12060164242600251039, "toml", false, 2743540424771778454], [13625485746686963219, "anyhow", false, 11686108123410396079], [15367738274754116744, "serde_json", false, 17612069096979246765], [15622660310229662834, "walkdir", false, 1123168135435355463], [17146114186171651583, "infer", false, 2843720288795253853], [17155886227862585100, "glob", false, 3512897230383724311], [17186037756130803222, "phf", false, 16461635104770261604]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-700ab91ad22ce000\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}