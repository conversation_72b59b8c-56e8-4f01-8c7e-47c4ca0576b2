{"name": "tauri-plugin-mpv", "version": "0.1.0", "author": "22", "description": "", "type": "module", "types": "./dist-js/index.d.ts", "main": "./dist-js/index.cjs", "module": "./dist-js/index.js", "exports": {"types": "./dist-js/index.d.ts", "import": "./dist-js/index.js", "require": "./dist-js/index.cjs"}, "files": ["dist-js", "README.md"], "scripts": {"build": "rollup -c", "prepublishOnly": "npm run build", "pretest": "npm run build"}, "dependencies": {"@tauri-apps/api": "2"}, "devDependencies": {"@rollup/plugin-typescript": "^12.1.4", "rollup": "^4.9.6", "typescript": "^5.3.3", "tslib": "^2.6.2"}}