{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[15963346172429945013, "build_script_build", false, 16265900210297665909], [8427153362654230442, "build_script_build", false, 17582842390601078653], [2979645065415301498, "build_script_build", false, 3296537402877390978], [14525517306681678134, "build_script_build", false, 5265775897584397395], [14316330136333005521, "build_script_build", false, 10603961309110473553], [16702348383442838006, "build_script_build", false, 9812809946682598504]], "local": [{"RerunIfChanged": {"output": "debug\\build\\mpv_tauri-24a4140d2289e2c1\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}