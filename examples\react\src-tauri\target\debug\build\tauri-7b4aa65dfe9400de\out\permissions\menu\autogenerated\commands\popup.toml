# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-popup"
description = "Enables the popup command without any pre-configured scope."
commands.allow = ["popup"]

[[permission]]
identifier = "deny-popup"
description = "Denies the popup command without any pre-configured scope."
commands.deny = ["popup"]
