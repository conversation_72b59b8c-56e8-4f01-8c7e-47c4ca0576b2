# MPV Configuration Guide

## Overview

The Tauri MPV Plugin now supports passing custom MPV configuration options during initialization. This allows you to customize MPV's behavior, video quality, audio settings, and more without needing to modify MPV configuration files.

## Basic Usage

### TypeScript/JavaScript

```typescript
import { initializeMpv, MpvConfig } from 'tauri-plugin-mpv';

// Basic initialization with configuration
const config: MpvConfig = {
  'vo': 'gpu',
  'hwdec': 'auto',
  'volume': 75,
  'loop-file': 'inf'
};

const windowLabel = await initializeMpv(
  ['filename', 'pause', 'time-pos'], // observed properties
  'main',                            // window label
  config                             // MPV configuration
);
```

### React Hook

```typescript
import { usePlayerWithConfig, HIGH_QUALITY_CONFIG } from './hooks/usePlayerWithConfig';

function MyComponent() {
  const player = usePlayerWithConfig(HIGH_QUALITY_CONFIG);
  
  // Use player as normal...
}
```

## Configuration Options

### Video Quality

```typescript
const highQualityConfig: MpvConfig = {
  'vo': 'gpu',                    // GPU-accelerated video output
  'hwdec': 'auto',               // Hardware decoding
  'profile': 'gpu-hq',           // High-quality profile
  'scale': 'ewa_lanczossharp',   // High-quality upscaling
  'cscale': 'ewa_lanczossharp',  // High-quality chroma upscaling
  'video-sync': 'display-resample', // Smooth playback
  'interpolation': true,         // Frame interpolation
  'tscale': 'oversample'         // Temporal scaling
};
```

### Audio Settings

```typescript
const audioConfig: MpvConfig = {
  'volume': 75,                     // Default volume (0-100)
  'volume-max': 150,                // Maximum volume
  'audio-channels': 'stereo',       // Channel layout
  'audio-normalize-downmix': true,  // Normalize audio
  'audio-pitch-correction': true,   // Maintain pitch when changing speed
  'af': 'dynaudnorm'               // Audio filter for dynamic normalization
};
```

### Playback Control

```typescript
const playbackConfig: MpvConfig = {
  'loop-file': 'inf',        // Loop current file infinitely
  'loop-playlist': true,     // Loop entire playlist
  'keep-open': true,         // Keep window open after playback ends
  'speed': 1.25,            // Default playback speed
  'pause': false,           // Start playing immediately
  'autofit': '80%'          // Window size as percentage of screen
};
```

### Subtitle Settings

```typescript
const subtitleConfig: MpvConfig = {
  'sub-auto': 'fuzzy',                    // Auto-load subtitle files
  'sub-file-paths': 'subs:subtitles',    // Subtitle search paths
  'sub-font-size': 55,                   // Subtitle font size
  'sub-color': '#FFFFFFFF',              // Subtitle color (white)
  'sub-border-color': '#FF000000',       // Border color (black)
  'sub-border-size': 3.2,                // Border thickness
  'sub-shadow-offset': 1,                // Shadow offset
  'sub-shadow-color': '#33000000'        // Shadow color
};
```

### Performance Optimization

```typescript
const performanceConfig: MpvConfig = {
  'vo': 'gpu',              // Use GPU
  'hwdec': 'auto',         // Hardware decoding
  'profile': 'fast',       // Fast profile
  'scale': 'bilinear',     // Fast scaling
  'dscale': 'bilinear',    // Fast downscaling
  'cscale': 'bilinear',    // Fast chroma scaling
  'video-sync': 'audio',   // Sync to audio
  'framedrop': 'vo',       // Drop frames if needed
  'cache': false           // Disable cache for live streams
};
```

### Streaming/Network

```typescript
const streamingConfig: MpvConfig = {
  'cache': true,                    // Enable cache
  'demuxer-max-bytes': '150MiB',   // Cache size
  'demuxer-readahead-secs': 10,    // Read-ahead time
  'network-timeout': 30,           // Network timeout
  'user-agent': 'MyApp/1.0',      // Custom user agent
  'referrer': 'https://example.com', // HTTP referrer
  'force-seekable': false          // Don't force seekability for streams
};
```

## Predefined Configurations

The plugin provides several predefined configurations for common use cases:

### HIGH_QUALITY_CONFIG
Optimized for best video quality with GPU acceleration and advanced scaling algorithms.

### LOW_LATENCY_CONFIG
Optimized for streaming with minimal buffering and cache.

### AUDIO_FOCUSED_CONFIG
For audio-only playback without video rendering.

### SUBTITLE_CONFIG
Enhanced subtitle display with auto-loading and styling.

### PERFORMANCE_CONFIG
Fast playback optimized for lower-end devices.

## Configuration Value Types

MPV configuration supports different value types:

```typescript
const config: MpvConfig = {
  // String values
  'vo': 'gpu',
  'audio-device': 'auto',
  
  // Number values
  'volume': 75,
  'speed': 1.5,
  
  // Boolean values
  'loop-file': true,        // Enables the option
  'border': false,          // Disables the option (same as --no-border)
  'interpolation': true,    // Enables interpolation
  
  // Special values
  'loop-playlist': 'inf',   // Infinite loop
  'geometry': '50%:50%'     // Position string
};
```

## Advanced Examples

### Custom Video Filter Chain

```typescript
const filterConfig: MpvConfig = {
  'vf': 'hqdn3d,unsharp=5:5:1.0:5:5:0.0', // Denoise + sharpen
  'af': 'dynaudnorm,loudnorm'              // Audio normalization
};
```

### Multi-Monitor Setup

```typescript
const multiMonitorConfig: MpvConfig = {
  'fs-screen': 1,           // Fullscreen on monitor 1
  'geometry': '1920x1080',  // Specific resolution
  'autofit-larger': '90%',  // Max size relative to screen
  'autofit-smaller': '50%'  // Min size relative to screen
};
```

### Development/Debug Configuration

```typescript
const debugConfig: MpvConfig = {
  'msg-level': 'all=debug',     // Verbose logging
  'log-file': 'mpv.log',       // Log to file
  'osd-level': 3,              // Show all OSD info
  'stats-file': 'stats.txt'    // Performance statistics
};
```

## Error Handling

```typescript
try {
  const windowLabel = await initializeMpv(
    COMMON_PROPERTIES,
    'main',
    config
  );
  console.log('MPV initialized successfully');
} catch (error) {
  console.error('MPV initialization failed:', error);
  // Handle initialization failure
}
```

## Best Practices

1. **Start Simple**: Begin with basic configurations and add options as needed.

2. **Test Configurations**: Different systems may support different options.

3. **Profile-Based Configs**: Use MPV's built-in profiles when possible:
   ```typescript
   { 'profile': 'gpu-hq' }  // Instead of manually setting all HQ options
   ```

4. **Conditional Configs**: Adapt configuration based on system capabilities:
   ```typescript
   const config = {
     'vo': 'gpu',
     'hwdec': navigator.hardwareConcurrency > 4 ? 'auto' : 'no'
   };
   ```

5. **User Preferences**: Allow users to customize configurations:
   ```typescript
   const userConfig = getUserPreferences();
   const finalConfig = { ...DEFAULT_CONFIG, ...userConfig };
   ```

## Troubleshooting

### Common Issues

1. **Invalid Option Names**: Use exact MPV option names (check `mpv --list-options`)
2. **Type Mismatches**: Ensure values match expected types (string/number/boolean)
3. **Platform Differences**: Some options may not be available on all platforms
4. **Hardware Limitations**: GPU options require compatible hardware

### Debugging

Enable verbose logging to see what options are being applied:

```typescript
const debugConfig: MpvConfig = {
  'msg-level': 'all=debug',
  'terminal': true  // Show messages in terminal
};
```

## Reference

- [MPV Manual](https://mpv.io/manual/master/)
- [MPV Options List](https://mpv.io/manual/master/#options)
- [MPV Profiles](https://mpv.io/manual/master/#profiles)
- [Video Output Drivers](https://mpv.io/manual/master/#video-output-drivers)
- [Audio Output Drivers](https://mpv.io/manual/master/#audio-output-drivers)
